{"name": "@stdlib/string-base-format-tokenize", "version": "0.0.4", "description": "Tokenize a string into an array of string parts and format identifier objects.", "license": "Apache-2.0", "author": {"name": "The Stdlib Authors", "url": "https://github.com/stdlib-js/stdlib/graphs/contributors"}, "contributors": [{"name": "The Stdlib Authors", "url": "https://github.com/stdlib-js/stdlib/graphs/contributors"}], "main": "./lib", "directories": {"benchmark": "./benchmark", "doc": "./docs", "example": "./examples", "lib": "./lib", "test": "./test"}, "types": "./docs/types", "scripts": {"test": "make test", "test-cov": "make test-cov", "examples": "make examples", "benchmark": "make benchmark"}, "homepage": "https://stdlib.io", "repository": {"type": "git", "url": "git://github.com/stdlib-js/string-base-format-tokenize.git"}, "bugs": {"url": "https://github.com/stdlib-js/stdlib/issues"}, "dependencies": {}, "devDependencies": {"@stdlib/assert-is-array": "^0.0.x", "@stdlib/bench": "^0.0.x", "tape": "git+https://github.com/kgryte/tape.git#fix/globby", "istanbul": "^0.4.1", "tap-spec": "5.x.x"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "keywords": ["stdlib", "stdstring", "utilities", "utility", "utils", "util", "string", "str", "format", "formatting", "fmt", "tokens", "tokenize", "token"], "funding": {"type": "patreon", "url": "https://www.patreon.com/athan"}}