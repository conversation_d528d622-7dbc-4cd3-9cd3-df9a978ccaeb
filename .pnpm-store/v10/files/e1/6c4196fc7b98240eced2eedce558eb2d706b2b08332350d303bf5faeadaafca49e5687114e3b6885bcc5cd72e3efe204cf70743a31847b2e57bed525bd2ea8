<!--

@license Apache-2.0

Copyright (c) 2018 The Stdlib Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

-->

# isString

[![NPM version][npm-image]][npm-url] [![Build Status][test-image]][test-url] [![Coverage Status][coverage-image]][coverage-url] <!-- [![dependencies][dependencies-image]][dependencies-url] -->

> Test if a value is a string.

<section class="installation">

## Installation

```bash
npm install @stdlib/assert-is-string
```

</section>

<section class="usage">

## Usage

```javascript
var isString = require( '@stdlib/assert-is-string' );
```

#### isString( value )

Tests if a value is a `string`.

<!-- eslint-disable no-new-wrappers -->

```javascript
var bool = isString( 'beep' );
// returns true

bool = isString( new String( 'beep' ) );
// returns true
```

#### isString.isPrimitive( value )

Tests if a `value` is a primitive `string`.

<!-- eslint-disable no-new-wrappers -->

```javascript
var bool = isString.isPrimitive( 'beep' );
// returns true

bool = isString.isPrimitive( new String( 'boop' ) );
// returns false
```

#### isString.isObject( value )

Tests if a `value` is a `String` object.

<!-- eslint-disable no-new-wrappers -->

```javascript
var bool = isString.isObject( 'beep' );
// returns false

bool = isString.isObject( new String( 'boop' ) );
// returns true
```

</section>

<!-- /.usage -->

<section class="examples">

## Examples

<!-- eslint-disable no-new-wrappers, no-restricted-syntax, no-empty-function -->

<!-- eslint no-undef: "error" -->

```javascript
var isString = require( '@stdlib/assert-is-string' );

var bool = isString( 'beep' );
// returns true

bool = isString( new String( 'beep' ) );
// returns true

bool = isString( 5 );
// returns false

bool = isString( null );
// returns false

bool = isString( void 0 );
// returns false

bool = isString( {} );
// returns false

bool = isString( [] );
// returns false

bool = isString( function foo() {} );
// returns false
```

</section>

<!-- /.examples -->

<!-- Section for related `stdlib` packages. Do not manually edit this section, as it is automatically populated. -->

<section class="related">

</section>

<!-- /.related -->

<!-- Section for all links. Make sure to keep an empty line after the `section` element and another before the `/section` close. -->


<section class="main-repo" >

* * *

## Notice

This package is part of [stdlib][stdlib], a standard library for JavaScript and Node.js, with an emphasis on numerical and scientific computing. The library provides a collection of robust, high performance libraries for mathematics, statistics, streams, utilities, and more.

For more information on the project, filing bug reports and feature requests, and guidance on how to develop [stdlib][stdlib], see the main project [repository][stdlib].

#### Community

[![Chat][chat-image]][chat-url]

---

## License

See [LICENSE][stdlib-license].


## Copyright

Copyright &copy; 2016-2022. The Stdlib [Authors][stdlib-authors].

</section>

<!-- /.stdlib -->

<!-- Section for all links. Make sure to keep an empty line after the `section` element and another before the `/section` close. -->

<section class="links">

[npm-image]: http://img.shields.io/npm/v/@stdlib/assert-is-string.svg
[npm-url]: https://npmjs.org/package/@stdlib/assert-is-string

[test-image]: https://github.com/stdlib-js/assert-is-string/actions/workflows/test.yml/badge.svg?branch=v0.0.8
[test-url]: https://github.com/stdlib-js/assert-is-string/actions/workflows/test.yml?query=branch:v0.0.8

[coverage-image]: https://img.shields.io/codecov/c/github/stdlib-js/assert-is-string/main.svg
[coverage-url]: https://codecov.io/github/stdlib-js/assert-is-string?branch=main

<!--

[dependencies-image]: https://img.shields.io/david/stdlib-js/assert-is-string.svg
[dependencies-url]: https://david-dm.org/stdlib-js/assert-is-string/main

-->

[chat-image]: https://img.shields.io/gitter/room/stdlib-js/stdlib.svg
[chat-url]: https://gitter.im/stdlib-js/stdlib/

[stdlib]: https://github.com/stdlib-js/stdlib

[stdlib-authors]: https://github.com/stdlib-js/stdlib/graphs/contributors

[umd]: https://github.com/umdjs/umd
[es-module]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Modules

[deno-url]: https://github.com/stdlib-js/assert-is-string/tree/deno
[umd-url]: https://github.com/stdlib-js/assert-is-string/tree/umd
[esm-url]: https://github.com/stdlib-js/assert-is-string/tree/esm
[branches-url]: https://github.com/stdlib-js/assert-is-string/blob/main/branches.md

[stdlib-license]: https://raw.githubusercontent.com/stdlib-js/assert-is-string/main/LICENSE

</section>

<!-- /.links -->
