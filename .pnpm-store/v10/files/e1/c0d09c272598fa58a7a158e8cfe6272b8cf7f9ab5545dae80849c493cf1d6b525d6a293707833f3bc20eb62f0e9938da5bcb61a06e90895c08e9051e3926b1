{"version": 3, "file": "getCurrentHubShim.js", "sources": ["../../src/getCurrentHubShim.ts"], "sourcesContent": ["import type { Client, EventHint, Hub, Integration, IntegrationClass, SeverityLevel } from '@sentry/types';\nimport { addBreadcrumb } from './breadcrumbs';\nimport { getClient, getCurrentScope, getIsolationScope, withScope } from './currentScopes';\nimport {\n  captureEvent,\n  endSession,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  startSession,\n} from './exports';\n\n/**\n * This is for legacy reasons, and returns a proxy object instead of a hub to be used.\n *\n * @deprecated Use the methods directly from the top level Sentry API (e.g. `Sentry.withScope`)\n * For more information see our migration guide for\n * [replacing `getCurrentHub` and `Hub`](https://github.com/getsentry/sentry-javascript/blob/develop/MIGRATION.md#deprecate-hub)\n * usage\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function getCurrentHubShim(): Hub {\n  return {\n    bindClient(client: Client): void {\n      const scope = getCurrentScope();\n      scope.setClient(client);\n    },\n\n    withScope,\n    getClient: <C extends Client>() => getClient() as C | undefined,\n    getScope: getCurrentScope,\n    getIsolationScope,\n    captureException: (exception: unknown, hint?: EventHint) => {\n      return getCurrentScope().captureException(exception, hint);\n    },\n    captureMessage: (message: string, level?: SeverityLevel, hint?: EventHint) => {\n      return getCurrentScope().captureMessage(message, level, hint);\n    },\n    captureEvent,\n    addBreadcrumb,\n    setUser,\n    setTags,\n    setTag,\n    setExtra,\n    setExtras,\n    setContext,\n\n    getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n      const client = getClient();\n      return (client && client.getIntegrationByName<T>(integration.id)) || null;\n    },\n\n    startSession,\n    endSession,\n    captureSession(end?: boolean): void {\n      // both send the update and pull the session from the scope\n      if (end) {\n        return endSession();\n      }\n\n      // only send the update\n      _sendSessionUpdate();\n    },\n  };\n}\n\n/**\n * Returns the default hub instance.\n *\n * If a hub is already registered in the global carrier but this module\n * contains a more recent version, it replaces the registered version.\n * Otherwise, the currently registered hub will be returned.\n *\n * @deprecated Use the respective replacement method directly instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const getCurrentHub = getCurrentHubShim;\n\n/**\n * Sends the current Session on the scope\n */\nfunction _sendSessionUpdate(): void {\n  const scope = getCurrentScope();\n  const client = getClient();\n\n  const session = scope.getSession();\n  if (client && session) {\n    client.captureSession(session);\n  }\n}\n"], "names": [], "mappings": ";;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,GAAQ;AACzC,EAAE,OAAO;AACT,IAAI,UAAU,CAAC,MAAM,EAAgB;AACrC,MAAM,MAAM,KAAA,GAAQ,eAAe,EAAE,CAAA;AACrC,MAAM,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AAC7B,KAAK;AACL;AACA,IAAI,SAAS;AACb,IAAI,SAAS,EAAE,MAAwB,SAAS,EAAG;AACnD,IAAI,QAAQ,EAAE,eAAe;AAC7B,IAAI,iBAAiB;AACrB,IAAI,gBAAgB,EAAE,CAAC,SAAS,EAAW,IAAI,KAAiB;AAChE,MAAM,OAAO,eAAe,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;AAChE,KAAK;AACL,IAAI,cAAc,EAAE,CAAC,OAAO,EAAU,KAAK,EAAkB,IAAI,KAAiB;AAClF,MAAM,OAAO,eAAe,EAAE,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;AACnE,KAAK;AACL,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,UAAU;AACd;AACA,IAAI,cAAc,CAAwB,WAAW,EAAiC;AACtF,MAAM,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAChC,MAAM,OAAO,CAAC,MAAO,IAAG,MAAM,CAAC,oBAAoB,CAAI,WAAW,CAAC,EAAE,CAAC,KAAK,IAAI,CAAA;AAC/E,KAAK;AACL;AACA,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,cAAc,CAAC,GAAG,EAAkB;AACxC;AACA,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,OAAO,UAAU,EAAE,CAAA;AAC3B,OAAM;AACN;AACA;AACA,MAAM,kBAAkB,EAAE,CAAA;AAC1B,KAAK;AACL,GAAG,CAAA;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,aAAc,GAAE,kBAAiB;AAC9C;AACA;AACA;AACA;AACA,SAAS,kBAAkB,GAAS;AACpC,EAAE,MAAM,KAAA,GAAQ,eAAe,EAAE,CAAA;AACjC,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B;AACA,EAAE,MAAM,OAAQ,GAAE,KAAK,CAAC,UAAU,EAAE,CAAA;AACpC,EAAE,IAAI,MAAO,IAAG,OAAO,EAAE;AACzB,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;AAClC,GAAE;AACF;;;;"}