{"name": "is-installed-globally", "version": "1.0.0", "description": "Check if your package was installed globally", "license": "MIT", "repository": "sindresorhus/is-installed-globally", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["global", "package", "globally", "module", "install", "installed", "npm", "yarn", "is", "check", "detect", "local", "locally", "cli", "bin", "binary"], "dependencies": {"global-directory": "^4.0.1", "is-path-inside": "^4.0.0"}, "devDependencies": {"ava": "^5.3.1", "cpy": "^8", "del": "^7.1.0", "execa": "^8.0.1", "make-dir": "^4.0.0", "tsd": "^0.29.0", "xo": "^0.56.0"}}