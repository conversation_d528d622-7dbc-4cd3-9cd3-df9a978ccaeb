{"version": 3, "file": "bindReporter.js", "sources": ["../../../../../src/metrics/web-vitals/lib/bindReporter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { MetricRatingThresholds, MetricType } from '../types';\n\nconst getRating = (value: number, thresholds: MetricRatingThresholds): MetricType['rating'] => {\n  if (value > thresholds[1]) {\n    return 'poor';\n  }\n  if (value > thresholds[0]) {\n    return 'needs-improvement';\n  }\n  return 'good';\n};\n\nexport const bindReporter = <MetricName extends MetricType['name']>(\n  callback: (metric: Extract<MetricType, { name: MetricName }>) => void,\n  metric: Extract<MetricType, { name: MetricN<PERSON> }>,\n  thresholds: MetricRatingThresholds,\n  reportAllChanges?: boolean,\n) => {\n  let prevValue: number;\n  let delta: number;\n  return (forceReport?: boolean) => {\n    if (metric.value >= 0) {\n      if (forceReport || reportAllChanges) {\n        delta = metric.value - (prevValue || 0);\n\n        // Report the metric if there's a non-zero delta or if no previous\n        // value exists (which can happen in the case of the document becoming\n        // hidden when the metric value is 0).\n        // See: https://github.com/GoogleChrome/web-vitals/issues/14\n        if (delta || prevValue === undefined) {\n          prevValue = metric.value;\n          metric.delta = delta;\n          metric.rating = getRating(metric.value, thresholds);\n          callback(metric);\n        }\n      }\n    }\n  };\n};\n"], "names": [], "mappings": ";;AAkBA,MAAM,YAAY,CAAC,KAAK,EAAU,UAAU,KAAmD;AAC/F,EAAE,IAAI,KAAM,GAAE,UAAU,CAAC,CAAC,CAAC,EAAE;AAC7B,IAAI,OAAO,MAAM,CAAA;AACjB,GAAE;AACF,EAAE,IAAI,KAAM,GAAE,UAAU,CAAC,CAAC,CAAC,EAAE;AAC7B,IAAI,OAAO,mBAAmB,CAAA;AAC9B,GAAE;AACF,EAAE,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AACD;AACO,MAAM,eAAe;AAC5B,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,UAAU;AACZ,EAAE,gBAAgB;AAClB,KAAK;AACL,EAAE,IAAI,SAAS,CAAA;AACf,EAAE,IAAI,KAAK,CAAA;AACX,EAAE,OAAO,CAAC,WAAW,KAAe;AACpC,IAAI,IAAI,MAAM,CAAC,KAAM,IAAG,CAAC,EAAE;AAC3B,MAAM,IAAI,WAAY,IAAG,gBAAgB,EAAE;AAC3C,QAAQ,KAAA,GAAQ,MAAM,CAAC,KAAA,IAAS,SAAA,IAAa,CAAC,CAAC,CAAA;AAC/C;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,KAAA,IAAS,SAAU,KAAI,SAAS,EAAE;AAC9C,UAAU,SAAU,GAAE,MAAM,CAAC,KAAK,CAAA;AAClC,UAAU,MAAM,CAAC,KAAM,GAAE,KAAK,CAAA;AAC9B,UAAU,MAAM,CAAC,MAAA,GAAS,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;AAC7D,UAAU,QAAQ,CAAC,MAAM,CAAC,CAAA;AAC1B,SAAQ;AACR,OAAM;AACN,KAAI;AACJ,GAAG,CAAA;AACH;;;;"}