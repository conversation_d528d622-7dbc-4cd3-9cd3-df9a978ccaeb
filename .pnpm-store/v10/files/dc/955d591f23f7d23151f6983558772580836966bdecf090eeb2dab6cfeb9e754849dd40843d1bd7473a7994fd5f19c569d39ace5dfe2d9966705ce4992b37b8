{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/plugins/validation/index.ts"], "names": [], "mappings": ";;;;AAIA,SAAgB,QAAQ,CAAC,GAAY;IACnC,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAA;AAChC,CAAC;AAFD,4BAEC;AAED,SAAgB,QAAQ,CAAC,GAAY;IACnC,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAA;AAChC,CAAC;AAFD,4BAEC;AAED,SAAgB,UAAU,CAAC,GAAY;IACrC,OAAO,OAAO,GAAG,KAAK,UAAU,CAAA;AAClC,CAAC;AAFD,gCAEC;AAED,SAAgB,aAAa,CAAC,GAAY;IACxC,OAAO,CACL,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,CAC5E,CAAA;AACH,CAAC;AAJD,sCAIC;AAED,SAAS,OAAO,CAAC,KAAmB;;IAClC,IAAM,EAAE,GACN,MAAA,MAAA,MAAA,KAAK,CAAC,MAAM,mCAAI,KAAK,CAAC,WAAW,mCAAI,KAAK,CAAC,OAAO,mCAAI,KAAK,CAAC,UAAU,CAAA;IACxE,OAAO,QAAQ,CAAC,EAAE,CAAC,CAAA;AACrB,CAAC;AAED;IAA8B,2CAAK;IAGjC,yBAAY,KAAa,EAAE,OAAe;QAA1C,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,KAAI,CAAC,KAAK,GAAG,KAAK,CAAA;;IACpB,CAAC;IACH,sBAAC;AAAD,CAAC,AAPD,CAA8B,KAAK,GAOlC;AAED,SAAS,QAAQ,CAAC,GAAY;;IAC5B,IAAM,SAAS,GAAY,GAAG,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAA;IAC7D,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAA;IAEvB,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,MAAM,IAAI,eAAe,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAA;KACvD;IAED,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACxB,MAAM,IAAI,eAAe,CAAC,OAAO,EAAE,uBAAuB,CAAC,CAAA;KAC5D;IAED,IAAI,SAAS,KAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACnD,MAAM,IAAI,eAAe,CAAC,OAAO,EAAE,uBAAuB,CAAC,CAAA;KAC5D;IAED,IAAM,KAAK,GAAG,MAAA,KAAK,CAAC,UAAU,mCAAI,KAAK,CAAC,MAAM,CAAA;IAC9C,IAAI,SAAS,KAAK,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;QAClD,MAAM,IAAI,eAAe,CAAC,YAAY,EAAE,6BAA6B,CAAC,CAAA;KACvE;IAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACnB,MAAM,IAAI,eAAe,CAAC,QAAQ,EAAE,+BAA+B,CAAC,CAAA;KACrE;IAED,OAAO,GAAG,CAAA;AACZ,CAAC;AAEY,QAAA,UAAU,GAAW;IAChC,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,QAAQ;IACd,OAAO,EAAE,OAAO;IAEhB,QAAQ,EAAE,cAAM,OAAA,IAAI,EAAJ,CAAI;IACpB,IAAI,EAAE,cAAM,OAAA,OAAO,CAAC,OAAO,EAAE,EAAjB,CAAiB;IAE7B,KAAK,EAAE,QAAQ;IACf,QAAQ,EAAE,QAAQ;IAClB,IAAI,EAAE,QAAQ;IACd,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,MAAM,EAAE,QAAQ;CACjB,CAAA"}