import e from"postcss-selector-parser";const s="js-focus-visible",o=":focus-visible",creator=n=>{const t=Object.assign({preserve:!0,replaceWith:".focus-visible",disablePolyfillReadyClass:!1},n),r=e().astSync(t.replaceWith);return{postcssPlugin:"postcss-focus-visible",prepare(){const n=new WeakSet;return{Rule(l,{result:a}){if(n.has(l))return;if(!l.selector.toLowerCase().includes(o))return;const i=l.selectors.flatMap((n=>{if(!n.toLowerCase().includes(o))return[n];let i;try{i=e().astSync(n)}catch(e){return l.warn(a,`Failed to parse selector : "${n}" with message: "${e.message}"`),n}if(void 0===i)return[n];let c=!1;if(i.walkPseudos((e=>{e.value.toLowerCase()===o&&(e.nodes&&e.nodes.length||(c=!0,e.replaceWith(r.clone({}))))})),!c)return[n];const d=i.clone();if(!t.disablePolyfillReadyClass){var u,p,f,v,b;if(null!=(u=i.nodes)&&null!=(p=u[0])&&null!=(f=p.nodes)&&f.length)for(let o=0;o<i.nodes[0].nodes.length;o++){const n=i.nodes[0].nodes[o];if("combinator"===n.type||e.isPseudoElement(n)){i.nodes[0].insertBefore(n,e.className({value:s}));break}if(o===i.nodes[0].nodes.length-1){i.nodes[0].append(e.className({value:s}));break}}return null!=(v=i.nodes)&&null!=(b=v[0])&&b.nodes&&(d.nodes[0].prepend(e.combinator({value:" "})),d.nodes[0].prepend(e.className({value:s}))),[i.toString(),d.toString()]}return[i.toString()]}));i.join(",")!==l.selectors.join(",")&&(n.add(l),l.cloneBefore({selectors:i}),t.preserve||l.remove())}}}}};creator.postcss=!0;export{creator as default};
