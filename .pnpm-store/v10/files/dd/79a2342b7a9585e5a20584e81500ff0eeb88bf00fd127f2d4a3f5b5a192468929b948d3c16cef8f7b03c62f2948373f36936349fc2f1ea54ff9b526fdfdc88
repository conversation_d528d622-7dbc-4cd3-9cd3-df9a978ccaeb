{"version": 3, "file": "envelope.d.ts", "sourceRoot": "", "sources": ["../../src/envelope.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AACnD,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;AACnD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AACnD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AAC3C,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACrC,OAAO,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AAC9D,OAAO,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AACzD,OAAO,KAAK,EAAE,WAAW,EAAE,mBAAmB,EAAE,MAAM,UAAU,CAAC;AACjE,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACzC,OAAO,KAAK,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,WAAW,CAAC;AACtE,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAKvC,MAAM,MAAM,sBAAsB,GAAG;IACnC,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;IACvC,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,CAAC;AAIF,MAAM,MAAM,gBAAgB,GACxB,eAAe,GACf,aAAa,GACb,UAAU,GACV,SAAS,GACT,UAAU,GACV,aAAa,GACb,YAAY,GACZ,OAAO,GACP,SAAS,GACT,eAAe,GACf,cAAc,GACd,kBAAkB,GAClB,UAAU,GACV,QAAQ,GACR,MAAM,CAAC;AAEX,MAAM,MAAM,mBAAmB,GAAG;IAChC,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;IACvB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,GAAG,CAAC,EAAE,OAAO,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,uBAAuB,GAAG;IACpC,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;IACvB,IAAI,EAAE,gBAAgB,CAAC;IACvB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,KAAK,gBAAgB,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU,GAAG,uBAAuB,EAAE,CAAC,CAAC,CAAC;AAEjF,KAAK,YAAY,CAAC,cAAc,EAAE,IAAI,IAAI;IACxC,cAAc,GAAG,mBAAmB;IACpC,KAAK,CAAC,IAAI,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;CACjE,CAAC;AAEF,KAAK,gBAAgB,GAAG;IACtB,IAAI,EAAE,OAAO,GAAG,aAAa,GAAG,SAAS,GAAG,UAAU,CAAC;CACxD,CAAC;AACF,KAAK,qBAAqB,GAAG;IAC3B,IAAI,EAAE,YAAY,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,eAAe,CAAC,EAAE,cAAc,CAAC;CAClC,CAAC;AACF,KAAK,uBAAuB,GAAG;IAAE,IAAI,EAAE,aAAa,CAAA;CAAE,CAAC;AACvD,KAAK,mBAAmB,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC;AAChD,KAAK,kBAAkB,GAAG;IAAE,IAAI,EAAE,SAAS,CAAA;CAAE,CAAC;AAC9C,KAAK,4BAA4B,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC;AACzD,KAAK,uBAAuB,GAAG;IAAE,IAAI,EAAE,eAAe,CAAA;CAAE,CAAC;AACzD,KAAK,sBAAsB,GAAG;IAAE,IAAI,EAAE,cAAc,CAAA;CAAE,CAAC;AACvD,KAAK,0BAA0B,GAAG;IAAE,IAAI,EAAE,kBAAkB,CAAC;IAAC,MAAM,EAAE,MAAM,CAAA;CAAE,CAAC;AAC/E,KAAK,kBAAkB,GAAG;IAAE,IAAI,EAAE,UAAU,CAAA;CAAE,CAAC;AAC/C,KAAK,kBAAkB,GAAG;IAAE,IAAI,EAAE,SAAS,CAAA;CAAE,CAAC;AAC9C,KAAK,uBAAuB,GAAG;IAAE,IAAI,EAAE,eAAe,CAAA;CAAE,CAAC;AACzD,KAAK,iBAAiB,GAAG;IAAE,IAAI,EAAE,QAAQ,CAAC;IAAC,MAAM,EAAE,MAAM,CAAA;CAAE,CAAC;AAC5D,KAAK,eAAe,GAAG;IAAE,IAAI,EAAE,MAAM,CAAA;CAAE,CAAC;AAExC,MAAM,MAAM,SAAS,GAAG,gBAAgB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AAClE,MAAM,MAAM,cAAc,GAAG,gBAAgB,CAAC,qBAAqB,EAAE,MAAM,GAAG,UAAU,CAAC,CAAC;AAC1F,MAAM,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;AACvF,MAAM,MAAM,WAAW,GACnB,gBAAgB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,GACvD,gBAAgB,CAAC,4BAA4B,EAAE,iBAAiB,CAAC,CAAC;AACtE,MAAM,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;AACvF,MAAM,MAAM,WAAW,GAAG,gBAAgB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;AAClF,KAAK,eAAe,GAAG,gBAAgB,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC;AAC7E,KAAK,mBAAmB,GAAG,gBAAgB,CAAC,0BAA0B,EAAE,mBAAmB,CAAC,CAAC;AAC7F,MAAM,MAAM,UAAU,GAAG,gBAAgB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;AACrE,MAAM,MAAM,YAAY,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;AAChF,MAAM,MAAM,WAAW,GAAG,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;AACxE,MAAM,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;AACvF,MAAM,MAAM,QAAQ,GAAG,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAE5E,MAAM,MAAM,oBAAoB,GAAG;IAAE,QAAQ,EAAE,MAAM,CAAC;IAAC,OAAO,EAAE,MAAM,CAAC;IAAC,KAAK,CAAC,EAAE,sBAAsB,CAAA;CAAE,CAAC;AACzG,KAAK,sBAAsB,GAAG;IAAE,OAAO,EAAE,MAAM,CAAA;CAAE,CAAC;AAClD,KAAK,sBAAsB,GAAG;IAAE,KAAK,CAAC,EAAE,sBAAsB,CAAA;CAAE,CAAC;AACjE,KAAK,2BAA2B,GAAG,mBAAmB,CAAC;AACvD,KAAK,qBAAqB,GAAG,mBAAmB,CAAC;AACjD,KAAK,qBAAqB,GAAG,mBAAmB,CAAC;AACjD,KAAK,mBAAmB,GAAG,mBAAmB,GAAG;IAAE,KAAK,CAAC,EAAE,sBAAsB,CAAA;CAAE,CAAC;AAEpF,MAAM,MAAM,aAAa,GAAG,YAAY,CACtC,oBAAoB,EACpB,SAAS,GAAG,cAAc,GAAG,gBAAgB,GAAG,YAAY,GAAG,WAAW,CAC3E,CAAC;AACF,MAAM,MAAM,eAAe,GAAG,YAAY,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC;AAChF,MAAM,MAAM,oBAAoB,GAAG,YAAY,CAAC,2BAA2B,EAAE,gBAAgB,CAAC,CAAC;AAC/F,MAAM,MAAM,cAAc,GAAG,CAAC,qBAAqB,EAAE,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC,CAAC;AAC7F,MAAM,MAAM,eAAe,GAAG,YAAY,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC;AAChF,MAAM,MAAM,cAAc,GAAG,YAAY,CAAC,qBAAqB,EAAE,UAAU,CAAC,CAAC;AAC7E,MAAM,MAAM,YAAY,GAAG,YAAY,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;AACvE,MAAM,MAAM,oBAAoB,GAAG,YAAY,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;AAEvF,MAAM,MAAM,QAAQ,GAChB,aAAa,GACb,eAAe,GACf,oBAAoB,GACpB,oBAAoB,GACpB,cAAc,GACd,eAAe,GACf,cAAc,GACd,YAAY,CAAC;AAEjB,MAAM,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC"}