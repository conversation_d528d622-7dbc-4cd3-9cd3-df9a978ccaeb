
{{alias}}( value )
    Tests if a value is a Uint16Array.

    Parameters
    ----------
    value: any
        Value to test.

    Returns
    -------
    bool: boolean
        Boolean indicating whether value is a Uint16Array.

    Examples
    --------
    > var bool = {{alias}}( new {{alias:@stdlib/array/uint16}}( 10 ) )
    true
    > bool = {{alias}}( [] )
    false

    See Also
    --------

