{"version": 3, "file": "live.js", "sourceRoot": "", "sources": ["../../src/directives/live.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAgB,QAAQ,EAAE,OAAO,EAAC,MAAM,gBAAgB,CAAC;AAChE,OAAO,EACL,SAAS,EACT,SAAS,EAGT,QAAQ,GACT,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAC,kBAAkB,EAAE,iBAAiB,EAAC,MAAM,yBAAyB,CAAC;AAE9E,MAAM,aAAc,SAAQ,SAAS;IACnC,YAAY,QAAkB;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IACE,CAAC,CACC,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,QAAQ;YACnC,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS;YACpC,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,iBAAiB,CAC7C,EACD;YACA,MAAM,IAAI,KAAK,CACb,gEAAgE,CACjE,CAAC;SACH;QACD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;SACzE;IACH,CAAC;IAED,MAAM,CAAC,KAAc;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAEQ,MAAM,CAAC,IAAmB,EAAE,CAAC,KAAK,CAA4B;QACrE,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,OAAO,EAAE;YAC3C,OAAO,KAAK,CAAC;SACd;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAEvB,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,QAAQ,EAAE;YACnC,8DAA8D;YAC9D,IAAI,KAAK,KAAM,OAAe,CAAC,IAAI,CAAC,EAAE;gBACpC,OAAO,QAAQ,CAAC;aACjB;SACF;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,iBAAiB,EAAE;YACnD,IAAI,CAAC,CAAC,KAAK,KAAK,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;gBAC1C,OAAO,QAAQ,CAAC;aACjB;SACF;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS,EAAE;YAC3C,IAAI,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE;gBAChD,OAAO,QAAQ,CAAC;aACjB;SACF;QACD,sEAAsE;QACtE,yBAAyB;QACzB,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange, nothing} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\nimport {isSingleExpression, setCommittedValue} from '../directive-helpers.js';\n\nclass LiveDirective extends Directive {\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      !(\n        partInfo.type === PartType.PROPERTY ||\n        partInfo.type === PartType.ATTRIBUTE ||\n        partInfo.type === PartType.BOOLEAN_ATTRIBUTE\n      )\n    ) {\n      throw new Error(\n        'The `live` directive is not allowed on child or event bindings'\n      );\n    }\n    if (!isSingleExpression(partInfo)) {\n      throw new Error('`live` bindings can only contain a single expression');\n    }\n  }\n\n  render(value: unknown) {\n    return value;\n  }\n\n  override update(part: AttributePart, [value]: DirectiveParameters<this>) {\n    if (value === noChange || value === nothing) {\n      return value;\n    }\n    const element = part.element;\n    const name = part.name;\n\n    if (part.type === PartType.PROPERTY) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      if (value === (element as any)[name]) {\n        return noChange;\n      }\n    } else if (part.type === PartType.BOOLEAN_ATTRIBUTE) {\n      if (!!value === element.hasAttribute(name)) {\n        return noChange;\n      }\n    } else if (part.type === PartType.ATTRIBUTE) {\n      if (element.getAttribute(name) === String(value)) {\n        return noChange;\n      }\n    }\n    // Resets the part's value, causing its dirty-check to fail so that it\n    // always sets the value.\n    setCommittedValue(part);\n    return value;\n  }\n}\n\n/**\n * Checks binding values against live DOM values, instead of previously bound\n * values, when determining whether to update the value.\n *\n * This is useful for cases where the DOM value may change from outside of\n * lit-html, such as with a binding to an `<input>` element's `value` property,\n * a content editable elements text, or to a custom element that changes it's\n * own properties or attributes.\n *\n * In these cases if the DOM value changes, but the value set through lit-html\n * bindings hasn't, lit-html won't know to update the DOM value and will leave\n * it alone. If this is not what you want--if you want to overwrite the DOM\n * value with the bound value no matter what--use the `live()` directive:\n *\n * ```js\n * html`<input .value=${live(x)}>`\n * ```\n *\n * `live()` performs a strict equality check against the live DOM value, and if\n * the new value is equal to the live value, does nothing. This means that\n * `live()` should not be used when the binding will cause a type conversion. If\n * you use `live()` with an attribute binding, make sure that only strings are\n * passed in, or the binding will update every render.\n */\nexport const live = directive(LiveDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {LiveDirective};\n"]}