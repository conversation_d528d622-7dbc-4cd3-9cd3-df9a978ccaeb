{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/analytics/index.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,WAAW,EACX,eAAe,EACf,WAAW,EACX,WAAW,EACX,UAAU,EAKV,cAAc,EACf,MAAM,uBAAuB,CAAA;AAC9B,OAAO,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAA;AAEvD,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAA;AACpC,OAAO,EAAY,OAAO,EAAE,MAAM,yBAAyB,CAAA;AAC3D,OAAO,EAGL,YAAY,EACZ,IAAI,EAEJ,YAAY,EACb,MAAM,WAAW,CAAA;AAClB,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,WAAW,CAAA;AACvC,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAA;AACjD,OAAO,EACL,aAAa,EAEb,KAAK,EACL,EAAE,EACF,gBAAgB,EAChB,IAAI,EACJ,WAAW,EACZ,MAAM,SAAS,CAAA;AAIhB,OAAO,KAAK,EACV,iBAAiB,EACjB,wBAAwB,EACzB,MAAM,qCAAqC,CAAA;AAC5C,OAAO,KAAK,EACV,6BAA6B,EAC7B,kBAAkB,EACnB,MAAM,0BAA0B,CAAA;AAIjC,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,cAAc,CAAA;AAiB9D,MAAM,WAAW,iBAAiB;IAChC,QAAQ,EAAE,MAAM,CAAA;IAChB,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,OAAO,CAAC,EAAE,MAAM,EAAE,CAAA;IAClB,mBAAmB,CAAC,EAAE,wBAAwB,EAAE,CAAA;CACjD;AAED,MAAM,WAAW,WAAW;IAC1B;;;;OAIG;IACH,wBAAwB,CAAC,EAAE,OAAO,CAAA;IAClC;;;;;OAKG;IACH,wBAAwB,CAAC,EAAE,OAAO,CAAA;IAClC,eAAe,CAAC,EAAE,OAAO,CAAA;IACzB,MAAM,CAAC,EAAE,aAAa,CAAA;IACtB,IAAI,CAAC,EAAE,WAAW,CAAA;IAClB,KAAK,CAAC,EAAE,WAAW,CAAA;IACnB,YAAY,CAAC,EAAE,YAAY,CAAA;IAC3B,IAAI,CAAC,EAAE,IAAI,CAAA;IACX,UAAU,CAAC,EAAE,OAAO,CAAA;IACpB,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB;;OAEG;IACH,cAAc,CAAC,EACX,OAAO,GACP;QACE,GAAG,CAAC,EAAE,MAAM,CAAA;QACZ,GAAG,CAAC,EAAE,MAAM,CAAA;KACb,CAAA;CACN;AAGD,iBAAS,KAAK,CAAC,IAAI,EAAE,KAAK,QAEzB;AAED,qBAAa,SACX,SAAQ,OACR,YAAW,aAAa,EAAE,gBAAgB;IAE1C,SAAS,CAAC,QAAQ,EAAE,iBAAiB,CAAA;IACrC,OAAO,CAAC,KAAK,CAAM;IACnB,OAAO,CAAC,MAAM,CAAO;IACrB,OAAO,CAAC,YAAY,CAAc;IAClC,OAAO,CAAC,MAAM,CAAQ;IACtB,OAAO,CAAC,iBAAiB,CAEvB;IAEF,WAAW,UAAQ;IACnB,YAAY,EAAE,YAAY,CAAA;IAC1B,OAAO,EAAE,WAAW,CAAA;IACpB,KAAK,EAAE,UAAU,CAAA;gBAGf,QAAQ,EAAE,iBAAiB,EAC3B,OAAO,CAAC,EAAE,WAAW,EACrB,KAAK,CAAC,EAAE,UAAU,EAClB,IAAI,CAAC,EAAE,IAAI,EACX,KAAK,CAAC,EAAE,KAAK;IAsCf,IAAI,QAAO,IAAI,CAEd;IAED,IAAI,OAAO,IAAI,gBAAgB,CAE9B;IAEK,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC;IAgBrD,IAAI,CAAC,GAAG,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,eAAe,CAAC;IAkBnD,QAAQ,CAAC,GAAG,IAAI,EAAE,cAAc,GAAG,OAAO,CAAC,eAAe,CAAC;IAwBjE,KAAK,IAAI,KAAK;IACd,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC;IA2B/C,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC;IAcrD,MAAM,CAAC,GAAG,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,eAAe,CAAC;IAuBrD,UAAU,CAAC,GAAG,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC;IAOjD,SAAS,CAAC,GAAG,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC;IAOhD,WAAW,CAAC,GAAG,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC;IAOlD,SAAS,CAAC,GAAG,IAAI,EAAE,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC;IAOhD,QAAQ,CAAC,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;IAWhD,UAAU,CAAC,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;IAiBxD,KAAK,CAAC,MAAM,EAAE,OAAO,GAAG,SAAS;IASjC,KAAK,IAAI,IAAI;IAMb,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;YAIhB,SAAS;IAejB,mBAAmB,CAAC,EAAE,EAAE,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC;IAqBrE,wBAAwB,CACtB,eAAe,EAAE,MAAM,EACvB,GAAG,WAAW,EAAE,6BAA6B,EAAE,GAC9C,OAAO,CAAC,SAAS,CAAC;IAWrB,cAAc,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE;IAIzB,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAWpD;;;;;;;OAOG;IACH,GAAG,CAAC,mBAAmB,EAAE,CAAC,SAAS,EAAE,SAAS,KAAK,IAAI,GAAG,SAAS;IAK7D,KAAK,CACT,QAAQ,GAAE,QAA+D,GACxE,OAAO,CAAC,OAAO,CAAC;IAWnB,UAAU,IAAI,SAAS;IAMvB,SAAS,CAAC,GAAG,EAAE,YAAY,GAAG,YAAY;IAK1C,IAAI,qBAAqB,IAAI,MAAM,EAAE,CAGpC;IAED,IAAI,OAAO,IAAI,MAAM,CAEpB;IAGK,UAAU,CACd,SAAS,CAAC,EAAE,iBAAiB,EAC7B,QAAQ,CAAC,EAAE,WAAW,GACrB,OAAO,CAAC,SAAS,CAAC;IAKrB,IAAI,eAPU,iBAAiB,aAClB,WAAW,KACrB,QAAQ,SAAS,CAAC,CAKY;IAE3B,QAAQ,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;IAM/C,IAAI,OAAO,QAIV;IAED,IAAI,YAAY,sCA+Bf;IAED,GAAG,eAAQ;IACX,wBAAwB,eAAQ;IAChC,SAAS,eAAQ;IACjB,gBAAgB,eAAQ;IACxB,kBAAkB,eAAQ;IAC1B,cAAc,eAAQ;IACtB,mBAAmB,eAAQ;IAC3B,YAAY,eAAQ;IACpB,GAAG,eAAQ;IACX,cAAc,eAAQ;IAItB,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;CASjB"}