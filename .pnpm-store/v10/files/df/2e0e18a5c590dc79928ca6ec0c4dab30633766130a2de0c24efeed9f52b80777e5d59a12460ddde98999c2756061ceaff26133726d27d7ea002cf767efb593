{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,kBAAkB,EAAoB,MAAM,qBAAqB,CAAC;AAmC3E,MAAM,UAAU,mBAAmB,CAAC,OAAyB,EAAE,iBAAoC;IACjG,IAAI,iBAAiB,KAAK,UAAU,EAAE,CAAC;QACrC,OAAO;YACL,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,aAAa,EAAE;gBACb,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,wBAAwB,EAAE,OAAO,CAAC,wBAAwB;aAC3D;SACF,CAAC;IACJ,CAAC;SAAM,IAAI,iBAAiB,KAAK,MAAM,EAAE,CAAC;QACxC,OAAO;YACL,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,aAAa,EAAE;gBACb,IAAI,EAAE,MAAM;gBACZ,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;gBAC9C,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,aAAa,EAAE,OAAO,CAAC,aAAa;aACrC;SACF,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;IACzE,CAAC;AACH,CAAC;AAED,+BAA+B;AAC/B,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B,aAAa,EAAE,kBAAkB,CAAC,aAAa;IAC/C,KAAK,EAAE,kBAAkB,CAAC,KAAK;IAC/B,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE;IAClB,aAAa,EAAE;QACb,WAAW,EAAE,kBAAkB,CAAC,WAAW;QAC3C,kBAAkB,EAAE,SAAS;QAC7B,UAAU,EAAE,SAAS;QACrB,SAAS,EAAE,SAAS;QACpB,UAAU,EAAE,SAAS;QACrB,KAAK,EAAE,kBAAkB,CAAC,KAAK;QAC/B,aAAa,EAAE,kBAAkB,CAAC,aAAa;QAC/C,cAAc,EAAE,SAAS;QACzB,wBAAwB,EAAE,SAAS;KACpC;CAC4B,CAAC;AAUhC,MAAM,UAAU,eAAe,CAAC,MAAc;IAC5C,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,cAAc,CAAC,GAAG,CAAC;IAC7C,IAAI,aAAgD,CAAC;IACrD,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;QAC7C,aAAa,GAAG;YACd,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,MAAM,CAAC,aAAa,CAAC,WAAW,IAAI,cAAc,CAAC,aAAa,CAAC,WAAW;YACzF,kBAAkB,EAAE,MAAM,CAAC,aAAa,CAAC,kBAAkB,IAAI,cAAc,CAAC,aAAa,CAAC,kBAAkB;YAC9G,UAAU,EAAE,MAAM,CAAC,aAAa,CAAC,UAAU,IAAI,cAAc,CAAC,aAAa,CAAC,UAAU;YACtF,SAAS,EAAE,MAAM,CAAC,aAAa,CAAC,SAAS,IAAI,cAAc,CAAC,aAAa,CAAC,SAAS;YACnF,UAAU,EAAE,MAAM,CAAC,aAAa,CAAC,UAAU,IAAI,cAAc,CAAC,aAAa,CAAC,UAAU;YACtF,KAAK,EAAE,MAAM,CAAC,aAAa,CAAC,KAAK,IAAI,cAAc,CAAC,aAAa,CAAC,KAAK;YACvE,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,aAAa,IAAI,cAAc,CAAC,aAAa,CAAC,aAAa;YAC/F,cAAc,EAAE,MAAM,CAAC,aAAa,CAAC,cAAc,IAAI,cAAc,CAAC,aAAa,CAAC,cAAc;YAClG,GAAG;YACH,wBAAwB,EACtB,MAAM,CAAC,aAAa,CAAC,wBAAwB,IAAI,cAAc,CAAC,aAAa,CAAC,wBAAwB;YACxG,OAAO,EAAE,MAAM,CAAC,aAAa,CAAC,OAAO;SACtC,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,aAAa,GAAG;YACd,IAAI,EAAE,MAAM;YACZ,kBAAkB,EAAE,MAAM,CAAC,aAAa,CAAC,kBAAkB,IAAI,cAAc,CAAC,aAAa,CAAC,kBAAkB;YAC9G,KAAK,EAAE,MAAM,CAAC,aAAa,CAAC,KAAK,IAAI,cAAc,CAAC,aAAa,CAAC,KAAK;YACvE,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,aAAa,IAAI,cAAc,CAAC,aAAa,CAAC,aAAa;YAC/F,cAAc,EAAE,MAAM,CAAC,aAAa,CAAC,cAAc,IAAI,cAAc,CAAC,aAAa,CAAC,cAAc;YAClG,GAAG;SACJ,CAAC;IACJ,CAAC;IACD,OAAO;QACL,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,aAAa,EAAE,MAAM,CAAC,aAAa,IAAI,cAAc,CAAC,aAAa;QACnE,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK;QAC3C,GAAG;QACH,aAAa;KACd,CAAC;AACJ,CAAC"}