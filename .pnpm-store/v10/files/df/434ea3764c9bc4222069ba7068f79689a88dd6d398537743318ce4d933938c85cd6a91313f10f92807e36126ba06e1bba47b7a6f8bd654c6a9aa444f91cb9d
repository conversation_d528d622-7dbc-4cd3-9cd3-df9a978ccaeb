"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _index = _interopRequireDefault(require("../../formatInTimeZone/index.js"));

var _index2 = _interopRequireDefault(require("date-fns/fp/_lib/convertToFP/index.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

// This file is generated automatically by `scripts/build/fp.js`. Please, don't change it.
var formatInTimeZone = (0, _index2.default)(_index.default, 3);
var _default = formatInTimeZone;
exports.default = _default;
module.exports = exports.default;