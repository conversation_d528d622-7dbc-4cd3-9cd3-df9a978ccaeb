{"name": "color2k", "version": "2.0.3", "description": "a color parsing and manipulation lib served in roughly 2kB", "repository": {"type": "git", "url": "https://github.com/ricokahler/color2k.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "rico<PERSON><PERSON>@me.com", "url": "https://github.com/ricokahler"}, "keywords": ["color", "css-in-js", "tinycolor", "color-parser", "color-manipulation", "polished", "chroma-js"], "sideEffects": false, "main": "./dist/index.main.cjs.js", "unpkg": "./dist/index.unpkg.umd.js", "jsdelivr": "./dist/index.unpkg.umd.js", "module": "./dist/index.module.es.js", "types": "./dist/index.d.ts", "exports": {".": [{"types": "./dist/index.d.ts", "import": "./dist/index.exports.import.es.mjs", "default": "./dist/index.exports.require.cjs.js"}, "./dist/index.exports.require.cjs.js"], "./package.json": "./package.json"}, "scripts": {"build": "./scripts/build", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "prepare": "npm run build", "test": "jest", "website": "node ./scripts/website.js"}, "devDependencies": {"@babel/core": "7.22.11", "@babel/node": "7.22.10", "@babel/preset-env": "7.22.14", "@babel/preset-react": "7.22.5", "@babel/preset-typescript": "7.22.11", "@babel/register": "7.22.5", "@rollup/plugin-alias": "4.0.4", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-terser": "0.4.4", "@sanity/eslint-config-studio": "2.0.4", "@types/jest": "29.5.10", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "@types/showdown": "2.0.6", "autoprefixer": "10.4.16", "@babel/eslint-parser": "7.22.11", "babel-plugin-module-resolver": "5.0.0", "cssnano": "5.1.15", "eslint": "8.54.0", "jest": "29.7.0", "postcss": "8.4.31", "preact": "10.19.2", "preact-render-to-string": "5.2.6", "prettier": "2.8.8", "react": "18.2.0", "react-dom": "18.2.0", "rollup": "3.29.4", "semantic-release": "19.0.5", "showdown": "2.1.0", "typescript": "4.9.5"}}