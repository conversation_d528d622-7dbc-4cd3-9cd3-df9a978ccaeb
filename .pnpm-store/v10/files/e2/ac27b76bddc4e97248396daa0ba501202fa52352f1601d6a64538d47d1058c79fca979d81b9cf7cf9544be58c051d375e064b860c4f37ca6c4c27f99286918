"use strict";var e=require("@csstools/postcss-progressive-custom-properties"),r=require("postcss-value-parser");function onCSSFunction(e){const r=e.value.toLowerCase();if(!needsConversion("rgb"===r||"rgba"===r,e.nodes))return;const n=convertOldSyntaxToNewSyntaxBeforeTransform(e.nodes).slice().filter((e=>"comment"!==e.type&&"space"!==e.type));let t=null;if("hsl"===r||"hsla"===r?t=hslFunctionContents(n):"rgb"!==r&&"rgba"!==r||(t=rgbFunctionContents(n)),!t)return;if(n.length>3&&(!t.slash||!t.alpha))return;transformAlpha(e,t.slash,t.alpha);const[o,u]=channelNodes(t);e.nodes.splice(e.nodes.indexOf(o)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),e.nodes.splice(e.nodes.indexOf(u)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""})}function isNumericNode(e){if(!e||"word"!==e.type)return!1;if(!canParseAsUnit(e))return!1;const n=r.unit(e.value);return!!n&&!!n.number}function isNumericNodeHueLike(e){if(!e||"word"!==e.type)return!1;if(!canParseAsUnit(e))return!1;const n=r.unit(e.value);if(!n)return!1;const t=n.unit.toLowerCase();return!!n.number&&("deg"===t||"grad"===t||"rad"===t||"turn"===t||""===n.unit)}function isNumericNodePercentageOrNumber(e){if(!e||"word"!==e.type)return!1;if(!canParseAsUnit(e))return!1;const n=r.unit(e.value);return!!n&&("%"===n.unit||""===n.unit)}function isCalcNode(e){return e&&"function"===e.type&&"calc"===e.value.toLowerCase()}function isVarNode(e){return e&&"function"===e.type&&"var"===e.value.toLowerCase()}function isSlashNode(e){return e&&"div"===e.type&&"/"===e.value}function hslFunctionContents(e){if(!isNumericNodeHueLike(e[0]))return null;if(!isNumericNodePercentageOrNumber(e[1]))return null;if(!isNumericNodePercentageOrNumber(e[2]))return null;const n={h:r.unit(e[0].value),hNode:e[0],s:r.unit(e[1].value),sNode:e[1],l:r.unit(e[2].value),lNode:e[2]};return normalizeHueNode(n.h),""!==n.h.unit?null:(n.hNode.value=n.h.number,isSlashNode(e[3])&&(n.slash=e[3]),(isNumericNodePercentageOrNumber(e[4])||isCalcNode(e[4])||isVarNode(e[4]))&&(n.alpha=e[4]),n)}function rgbFunctionContents(e){if(!isNumericNodePercentageOrNumber(e[0]))return null;if(!isNumericNodePercentageOrNumber(e[1]))return null;if(!isNumericNodePercentageOrNumber(e[2]))return null;const n={r:r.unit(e[0].value),rNode:e[0],g:r.unit(e[1].value),gNode:e[1],b:r.unit(e[2].value),bNode:e[2]};return"%"===n.r.unit&&(n.r.number=String(Math.floor(Number(n.r.number)/100*255)),n.rNode.value=n.r.number),"%"===n.g.unit&&(n.g.number=String(Math.floor(Number(n.g.number)/100*255)),n.gNode.value=n.g.number),"%"===n.b.unit&&(n.b.number=String(Math.floor(Number(n.b.number)/100*255)),n.bNode.value=n.b.number),isSlashNode(e[3])&&(n.slash=e[3]),(isNumericNodePercentageOrNumber(e[4])||isCalcNode(e[4])||isVarNode(e[4]))&&(n.alpha=e[4]),n}function isRgb(e){return void 0!==e.r}function channelNodes(e){return isRgb(e)?[e.rNode,e.gNode,e.bNode]:[e.hNode,e.sNode,e.lNode]}function transformAlpha(e,n,t){if("hsl"===e.value.toLowerCase()||"hsla"===e.value.toLowerCase()?e.value="hsl":"rgb"!==e.value.toLowerCase()&&"rgba"!==e.value.toLowerCase()||(e.value="rgb"),!n||!t)return;if("hsl"===e.value.toLowerCase()?e.value="hsla":e.value="rgba",n.value=",",n.before="",!isNumericNode(t))return;const o=r.unit(t.value);o&&"%"===o.unit&&(o.number=String(parseFloat(o.number)/100),t.value=String(o.number))}function normalizeHueNode(e){switch(e.unit.toLowerCase()){case"deg":return void(e.unit="");case"rad":return e.unit="",void(e.number=Math.round(180*parseFloat(e.number)/Math.PI).toString());case"grad":return e.unit="",void(e.number=Math.round(.9*parseFloat(e.number)).toString());case"turn":return e.unit="",void(e.number=Math.round(360*parseFloat(e.number)).toString())}}function canParseAsUnit(e){if(!e||!e.value)return!1;try{return!1!==r.unit(e.value)}catch(e){return!1}}function convertOldSyntaxToNewSyntaxBeforeTransform(e){let r=0;for(let n=0;n<e.length;n++){const t=e[n];if("div"===t.type&&","===t.value){if(r<2&&(t.value=" ",t.type="space"),2===r&&(t.value="/"),r>2)return[];r++}}return e}function needsConversion(e,r){let n=!1,t=!1,o=!1;const u=r.slice().filter((e=>"comment"!==e.type&&"space"!==e.type));for(let s=0;s<u.length;s++){const a=u[s];if("word"===a.type&&"from"===a.value.toLowerCase())return!1;"div"!==a.type||","!==a.value?e&&"word"===a.type&&a.value.endsWith("%")?o=!0:s===r.length-1&&"word"===a.type&&a.value.endsWith("%")&&(t=!0):n=!0}return!(!n||!t&&!o)||!n}const n=/(\(color: rgb(a?)\(0 0 0 \/ 0)|(\(color: hsl(a?)\(0 0% 0% \/ 0)/i;function hasSupportsAtRuleAncestor(e){let r=e.parent;for(;r;)if("atrule"===r.type){if("supports"===r.name&&n.test(r.params))return!0;r=r.parent}else r=r.parent;return!1}function hasFallback(e){const r=e.parent;if(!r)return!1;const n=e.prop.toLowerCase(),t=r.index(e);for(let e=0;e<t;e++){const t=r.nodes[e];if("decl"===t.type&&t.prop.toLowerCase()===n)return!0}return!1}const basePlugin=e=>({postcssPlugin:"postcss-color-function",Declaration:(n,{result:t})=>{const o=n.value,u=o.toLowerCase();if(!(u.includes("rgb")||u.includes("rgba")||u.includes("hsl")||u.includes("hsla")))return;if(hasFallback(n))return;if(hasSupportsAtRuleAncestor(n))return;let s;try{s=r(o)}catch(e){n.warn(t,`Failed to parse value '${o}' as a hsl or rgb function. Leaving the original value intact.`)}if(void 0===s)return;s.walk((e=>{if(!e.type||"function"!==e.type)return;const r=e.value.toLowerCase();"hsl"!==r&&"hsla"!==r&&"rgb"!==r&&"rgba"!==r||onCSSFunction(e)}));const a=String(s);a!==o&&(n.cloneBefore({value:a}),null!=e&&e.preserve||n.remove())}});basePlugin.postcss=!0;const postcssPlugin=r=>{const n=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},r);return n.enableProgressiveCustomProperties&&n.preserve?{postcssPlugin:"postcss-color-function",plugins:[e(),basePlugin(n)]}:basePlugin(n)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
