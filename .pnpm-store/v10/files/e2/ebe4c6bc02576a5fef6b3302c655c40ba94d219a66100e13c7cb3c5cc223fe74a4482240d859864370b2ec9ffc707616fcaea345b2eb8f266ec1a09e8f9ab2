{"version": 3, "file": "parse-cdn.js", "sourceRoot": "", "sources": ["../../../src/lib/parse-cdn.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAA;AAEvD,IAAM,oBAAoB,GACxB,uEAAuE,CAAA;AACzE,IAAM,sBAAsB,GAAG;IAC7B,IAAI,GAAuB,CAAA;IAC3B,IAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CACxC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CACpC,CAAA;IACD,OAAO,CAAC,OAAO,CAAC,UAAC,CAAC;;QAChB,IAAM,GAAG,GAAG,MAAA,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,mCAAI,EAAE,CAAA;QACvC,IAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAE7C,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;YACvB,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;SAChB;IACH,CAAC,CAAC,CAAA;IACF,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,IAAI,UAA8B,CAAA,CAAC,uCAAuC;AAC1E,IAAM,eAAe,GAAG;;IACtB,IAAM,MAAM,GAAG,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,MAAA,MAAM,CAAC,SAAS,0CAAE,IAAI,CAAA;IACnD,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAED,MAAM,CAAC,IAAM,eAAe,GAAG,UAAC,GAAW;IACzC,IAAI,MAAM,CAAC,SAAS,EAAE;QACpB,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,GAAG,CAAA;KAC5B;IACD,UAAU,GAAG,GAAG,CAAA;AAClB,CAAC,CAAA;AAED,MAAM,CAAC,IAAM,MAAM,GAAG;IACpB,IAAM,YAAY,GAAG,eAAe,EAAE,CAAA;IAEtC,IAAI,YAAY;QAAE,OAAO,YAAY,CAAA;IAErC,IAAM,gBAAgB,GAAG,sBAAsB,EAAE,CAAA;IAEjD,IAAI,gBAAgB,EAAE;QACpB,OAAO,gBAAgB,CAAA;KACxB;SAAM;QACL,+DAA+D;QAC/D,yCAAyC;QACzC,0CAA0C;QAC1C,wDAAwD;QACxD,OAAO,qBAAqB,CAAA;KAC7B;AACH,CAAC,CAAA;AAED,MAAM,CAAC,IAAM,sBAAsB,GAAG;IACpC,IAAM,GAAG,GAAG,MAAM,EAAE,CAAA;IACpB,OAAO,UAAG,GAAG,uBAAoB,CAAA;AACnC,CAAC,CAAA;AAED;;;;IAII;AACJ,MAAM,UAAU,gBAAgB;;IAC9B,IAAM,QAAQ,GAAG,MAAA,gBAAgB,EAAE,mCAAI,MAAM,CAAC,SAAS,CAAC,SAAS,CAAA;IAEjE,IAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CACxC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CACpC,CAAA;IACD,IAAI,IAAI,GAAuB,SAAS,CAAA;IAExC,KAAgB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;QAApB,IAAM,CAAC,gBAAA;QACV,IAAM,GAAG,GAAG,MAAA,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,mCAAI,EAAE,CAAA;QACvC,IAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAE7C,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE;YACvB,IAAI,GAAG,GAAG,CAAA;YACV,MAAK;SACN;KACF;IAED,IAAI,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAA;KAChE;IAED,OAAO,8CAAuC,QAAQ,0BAAuB,CAAA;AAC/E,CAAC"}