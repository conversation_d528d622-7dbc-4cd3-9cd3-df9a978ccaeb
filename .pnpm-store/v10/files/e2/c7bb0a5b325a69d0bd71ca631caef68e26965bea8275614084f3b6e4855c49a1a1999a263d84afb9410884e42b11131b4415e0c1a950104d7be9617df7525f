{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../src/browser/index.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAA;AAE7E,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAA;AAErC,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAA;AAI7D,OAAO,EAAgB,YAAY,EAAE,MAAM,0BAA0B,CAAA;AACrE,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAA;AAGhE,OAAO,EACL,iBAAiB,EAMlB,MAAM,gBAAgB,CAAA;AAMvB,MAAM,WAAW,8BAA8B;IAE7C,IAAI,CAAC,EAAE,MAAM,CAAA;IAEb,eAAe,CAAC,EAAE;QAChB,OAAO,CAAC,EAAE,MAAM,CAAA;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,cAAc,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,SAAS,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAA;KACjE,CAAA;IAED,cAAc,CAAC,EAAE,MAAM,CAAA;IAGvB,UAAU,CAAC,EAAE,OAAO,CAAA;IAIpB,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CACnB;AAED,MAAM,WAAW,cAAc;IAC7B,YAAY,EAAE;QACZ,CAAC,IAAI,EAAE,MAAM,GAAG,8BAA8B,CAAA;KAC/C,CAAA;IAED,kBAAkB,CAAC,EAAE;QACnB,YAAY,EAAE,WAAW,EAAE,CAAA;KAC5B,CAAA;IAED,iBAAiB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC3C,OAAO,CAAC,EAAE,cAAc,CAAA;IAExB,IAAI,CAAC,EAAE,IAAI,CAAA;IAEX,yBAAyB,CAAC,EAAE,OAAO,CAAA;IAEnC,aAAa,CAAC,EAAE,YAAY,EAAE,CAAA;CAC/B;AAED,MAAM,WAAW,wBAAyB,SAAQ,iBAAiB;IACjE;;;;OAIG;IACH,WAAW,CAAC,EAAE,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACtD;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAA;CAChB;AAED,wBAAgB,kBAAkB,CAChC,MAAM,CAAC,EAAE,MAAM,GACd,cAAc,CAOhB;AAwND;;;;;;;;;GASG;AACH,qBAAa,gBAAiB,SAAQ,iBAAiB;IACrD,OAAO,CAAC,iBAAiB,CAGhB;;IAgBT;;;;;;;;;;;;;;;OAeG;IACH,IAAI,CACF,QAAQ,EAAE,wBAAwB,EAClC,OAAO,GAAE,WAAgB,GACxB,gBAAgB;IAKnB;;;;;;;;;;OAUG;IACH,MAAM,CAAC,IAAI,CACT,QAAQ,EAAE,wBAAwB,EAClC,OAAO,GAAE,WAAgB,GACxB,gBAAgB;IAInB,MAAM,CAAC,UAAU,CACf,QAAQ,EAAE,MAAM,EAChB,OAAO,CAAC,EAAE,WAAW,GACpB,OAAO,CAAC,SAAS,CAAC;CAGtB"}