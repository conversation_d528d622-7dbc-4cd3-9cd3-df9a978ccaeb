{"name": "@stdlib/utils-library-manifest", "version": "0.0.8", "description": "Load a manifest for compiling source files.", "license": "Apache-2.0", "author": {"name": "The Stdlib Authors", "url": "https://github.com/stdlib-js/stdlib/graphs/contributors"}, "contributors": [{"name": "The Stdlib Authors", "url": "https://github.com/stdlib-js/stdlib/graphs/contributors"}], "bin": {"library-manifest": "./bin/cli"}, "main": "./lib", "directories": {"doc": "./docs", "example": "./examples", "lib": "./lib", "test": "./test"}, "types": "./docs/types", "scripts": {"test": "make test", "test-cov": "make test-cov", "examples": "make examples"}, "homepage": "https://stdlib.io", "repository": {"type": "git", "url": "git://github.com/stdlib-js/utils-library-manifest.git"}, "bugs": {"url": "https://github.com/stdlib-js/stdlib/issues"}, "dependencies": {"@stdlib/cli-ctor": "^0.0.x", "@stdlib/fs-resolve-parent-path": "^0.0.x", "@stdlib/utils-convert-path": "^0.0.x", "debug": "^2.6.9", "resolve": "^1.1.7"}, "devDependencies": {"@stdlib/assert-is-browser": "^0.0.x", "@stdlib/assert-is-plain-object": "^0.0.x", "@stdlib/assert-is-windows": "^0.0.x", "@stdlib/fs-read-file": "^0.0.x", "@stdlib/process-exec-path": "^0.0.x", "@stdlib/utils-keys": "^0.0.x", "tape": "git+https://github.com/kgryte/tape.git#fix/globby", "istanbul": "^0.4.1", "tap-spec": "5.x.x"}, "engines": {"node": ">=0.10.0", "npm": ">2.7.0"}, "os": ["aix", "darwin", "freebsd", "linux", "macos", "openbsd", "sunos", "win32", "windows"], "keywords": ["stdlib", "standard", "library", "std", "lib", "tools", "manifest", "compile", "package", "manager", "source", "files", "program", "load", "configuration", "config"], "__stdlib__": {"envs": {"browser": false}}, "funding": {"type": "patreon", "url": "https://www.patreon.com/athan"}}