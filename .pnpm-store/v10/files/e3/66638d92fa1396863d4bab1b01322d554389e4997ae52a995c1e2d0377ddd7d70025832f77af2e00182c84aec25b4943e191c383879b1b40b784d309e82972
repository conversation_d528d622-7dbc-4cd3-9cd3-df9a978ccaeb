<?xml version="1.0" encoding="UTF-8"?>
<MPD
  xmlns:cenc="urn:mpeg:cenc:2013"
  availabilityStartTime="2021-03-18T20:00:36Z"
  maxSegmentDuration="PT2S"
  minBufferTime="PT2S"
  minimumUpdatePeriod="PT2S"
  profiles="urn:mpeg:dash:profile:isoff-live:2011"
  publishTime="2021-03-18T20:32:55Z"
  suggestedPresentationDelay="PT6S"
  timeShiftBufferDepth="PT180.000S"
  type="dynamic"
  xmlns="urn:mpeg:dash:schema:mpd:2011">
    <Period id="111" start="PT111S">
        <AdaptationSet
          audioSamplingRate="48000"
          contentType="audio"
          group="1"
          lang="en"
          mimeType="audio/mp4"
          segmentAlignment="true"
          startWithSAP="1">
            <Role schemeIdUri="urn:mpeg:dash:role:2011" value="main" />
            <Representation
              bandwidth="129262"
              codecs="mp4a.40.5"
              id="v0">
                <AudioChannelConfiguration schemeIdUri="urn:mpeg:dash:23003:3:audio_channel_configuration:2011" value="2" />
                <BaseURL>http://example.com/audio/v0/</BaseURL>
                <SegmentTemplate
                  initialization="init.mp4"
                  media="$Number%03d$.m4f"
                  presentationTimeOffset="9989999"
                  startNumber="862"
                  timescale="90000">
                    <SegmentTimeline>
                        <S d="90000" r="2" t="9989999" />
                    </SegmentTimeline>
                </SegmentTemplate>
            </Representation>
        </AdaptationSet>
        <AdaptationSet
          contentType="video"
          id="1"
          maxFrameRate="60.0"
          maxHeight="720"
          maxWidth="1280"
          mimeType="video/mp4"
          segmentAlignment="true"
          startWithSAP="1">
            <SupplementalProperty schemeIdUri="urn:mpeg:dash:adaptation-set-switching:2016" value="2" />
            <Representation
              bandwidth="2942295"
              codecs="avc1.4d001f"
              frameRate="30.0"
              height="720"
              id="D"
              scanType="progressive"
              width="1280">
                <BaseURL>http://example.com/video/D/</BaseURL>
                <SegmentTemplate
                  initialization="$RepresentationID$_init.mp4"
                  media="$RepresentationID$$Number%03d$.m4f"
                  presentationTimeOffset="9989999"
                  startNumber="862"
                  timescale="90000">
                    <SegmentTimeline>
                        <S d="90000" r="2" t="9989999" />
                    </SegmentTimeline>
                </SegmentTemplate>
            </Representation>
            <Representation
              bandwidth="4267536"
              codecs="avc1.640020"
              frameRate="60.0"
              height="720"
              id="E"
              scanType="progressive"
              width="1280">
                <BaseURL>http://example.com/video/E/</BaseURL>
                <SegmentTemplate
                  initialization="$RepresentationID$_init.mp4"
                  media="$RepresentationID$$Number%03d$.m4f"
                  presentationTimeOffset="9989999"
                  startNumber="862"
                  timescale="90000">
                    <SegmentTimeline>
                        <S d="90000" r="2" t="9989999" />
                    </SegmentTimeline>
                </SegmentTemplate>
            </Representation>
            <Representation
              bandwidth="5256859"
              codecs="avc1.640020"
              frameRate="60.0"
              height="720"
              id="F"
              scanType="progressive"
              width="1280">
                <BaseURL>http://example.com/video/F/</BaseURL>
                <SegmentTemplate
                  initialization="$RepresentationID$_init.mp4"
                  media="$RepresentationID$$Number%03d$.m4f"
                  presentationTimeOffset="9989999"
                  startNumber="862"
                  timescale="90000">
                    <SegmentTimeline>
                        <S d="90000" r="2" t="9989999" />
                    </SegmentTimeline>
                </SegmentTemplate>
            </Representation>
        </AdaptationSet>
        <AdaptationSet
          contentType="video"
          id="2"
          maxFrameRate="30.0"
          maxHeight="540"
          maxWidth="960"
          mimeType="video/mp4"
          segmentAlignment="true"
          startWithSAP="1">
            <Representation
              bandwidth="240781"
              codecs="avc1.4d000d"
              frameRate="30.0"
              height="234"
              id="A"
              scanType="progressive"
              width="416">
                <BaseURL>http://example.com/video/A/</BaseURL>
                <SegmentTemplate
                  initialization="$RepresentationID$_init.mp4"
                  media="$RepresentationID$$Number%03d$.m4f"
                  presentationTimeOffset="9989999"
                  startNumber="862"
                  timescale="90000">
                    <SegmentTimeline>
                        <S d="90000" r="2" t="9989999" />
                    </SegmentTimeline>
                </SegmentTemplate>
            </Representation>
            <Representation
              bandwidth="494354"
              codecs="avc1.4d001e"
              frameRate="30.0"
              height="360"
              id="B"
              scanType="progressive"
              width="640">
                <BaseURL>http://example.com/video/B/</BaseURL>
                <SegmentTemplate
                  initialization="$RepresentationID$_init.mp4"
                  media="$RepresentationID$$Number%03d$.m4f"
                  presentationTimeOffset="9989999"
                  startNumber="862"
                  timescale="90000">
                    <SegmentTimeline>
                        <S d="90000" r="2" t="9989999" />
                    </SegmentTimeline>
                </SegmentTemplate>
            </Representation>
            <Representation
              bandwidth="1277155"
              codecs="avc1.4d001f"
              frameRate="30.0"
              height="540"
              id="C"
              scanType="progressive"
              width="960">
                <BaseURL>http://example.com/video/C/</BaseURL>
                <SegmentTemplate
                  initialization="$RepresentationID$_init.mp4"
                  media="$RepresentationID$$Number%03d$.m4f"
                  presentationTimeOffset="9989999"
                  startNumber="862"
                  timescale="90000">
                    <SegmentTimeline>
                        <S d="90000" r="2" t="9989999" />
                    </SegmentTimeline>
                </SegmentTemplate>
            </Representation>
        </AdaptationSet>
    </Period>
    <UTCTiming schemeIdUri="urn:mpeg:dash:utc:http-iso:2014" value="http://example.com//utcservertime" />
</MPD>
