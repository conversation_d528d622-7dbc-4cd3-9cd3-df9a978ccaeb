{
  "compilerOptions": {
    "target": "ESNext",
    // Volar
    "jsx": "preserve",
    "lib": [
      "ESNext",
      "DOM"
    ],
    "module": "ESNext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "types": [
      "node"
    ],
    "strictBindCallApply": true,
    "strictFunctionTypes": true,
    "alwaysStrict": true,
    // Strict
    "noImplicitAny": false,
    "noImplicitThis": true,
    "declaration": true,
    "outDir": "dist",
    "removeComments": false,
    "sourceMap": false,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "preserveWatchOutput": true
  },
  "include": [
    "src"
  ]
}
