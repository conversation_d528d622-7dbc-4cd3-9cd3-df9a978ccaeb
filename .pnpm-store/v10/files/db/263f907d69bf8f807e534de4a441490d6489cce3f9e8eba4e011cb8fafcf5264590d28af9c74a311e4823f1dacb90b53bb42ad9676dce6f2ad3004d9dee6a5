{"program": {"fileInfos": {"../node_modules/typescript/lib/lib.es5.d.ts": {"version": "fc43680ad3a1a4ec8c7b8d908af1ec9ddff87845346de5f02c735c9171fa98ea", "signature": "fc43680ad3a1a4ec8c7b8d908af1ec9ddff87845346de5f02c735c9171fa98ea"}, "../node_modules/typescript/lib/lib.es2015.d.ts": {"version": "7994d44005046d1413ea31d046577cdda33b8b2470f30281fd9c8b3c99fe2d96", "signature": "7994d44005046d1413ea31d046577cdda33b8b2470f30281fd9c8b3c99fe2d96"}, "../node_modules/typescript/lib/lib.es2016.d.ts": {"version": "5f217838d25704474d9ef93774f04164889169ca31475fe423a9de6758f058d1", "signature": "5f217838d25704474d9ef93774f04164889169ca31475fe423a9de6758f058d1"}, "../node_modules/typescript/lib/lib.es2017.d.ts": {"version": "459097c7bdd88fc5731367e56591e4f465f2c9de81a35427a7bd473165c34743", "signature": "459097c7bdd88fc5731367e56591e4f465f2c9de81a35427a7bd473165c34743"}, "../node_modules/typescript/lib/lib.dom.d.ts": {"version": "d93de5e8a7275cb9d47481410e13b3b1debb997e216490954b5d106e37e086de", "signature": "d93de5e8a7275cb9d47481410e13b3b1debb997e216490954b5d106e37e086de"}, "../node_modules/typescript/lib/lib.dom.iterable.d.ts": {"version": "8329c3401aa8708426c7760f14219170f69a2cb77e4519758cec6f5027270faf", "signature": "8329c3401aa8708426c7760f14219170f69a2cb77e4519758cec6f5027270faf"}, "../node_modules/typescript/lib/lib.webworker.importscripts.d.ts": {"version": "fe4e59403e34c7ff747abe4ff6abbc7718229556d7c1a5b93473fb53156c913b", "signature": "fe4e59403e34c7ff747abe4ff6abbc7718229556d7c1a5b93473fb53156c913b"}, "../node_modules/typescript/lib/lib.scripthost.d.ts": {"version": "b9faa17292f17d2ad75e34fac77dd63a6403af1dba02d39cd0cbb9ffdf3de8b9", "signature": "b9faa17292f17d2ad75e34fac77dd63a6403af1dba02d39cd0cbb9ffdf3de8b9"}, "../node_modules/typescript/lib/lib.es2015.core.d.ts": {"version": "734ddc145e147fbcd55f07d034f50ccff1086f5a880107665ec326fb368876f6", "signature": "734ddc145e147fbcd55f07d034f50ccff1086f5a880107665ec326fb368876f6"}, "../node_modules/typescript/lib/lib.es2015.collection.d.ts": {"version": "4a0862a21f4700de873db3b916f70e41570e2f558da77d2087c9490f5a0615d8", "signature": "4a0862a21f4700de873db3b916f70e41570e2f558da77d2087c9490f5a0615d8"}, "../node_modules/typescript/lib/lib.es2015.generator.d.ts": {"version": "765e0e9c9d74cf4d031ca8b0bdb269a853e7d81eda6354c8510218d03db12122", "signature": "765e0e9c9d74cf4d031ca8b0bdb269a853e7d81eda6354c8510218d03db12122"}, "../node_modules/typescript/lib/lib.es2015.iterable.d.ts": {"version": "285958e7699f1babd76d595830207f18d719662a0c30fac7baca7df7162a9210", "signature": "285958e7699f1babd76d595830207f18d719662a0c30fac7baca7df7162a9210"}, "../node_modules/typescript/lib/lib.es2015.promise.d.ts": {"version": "d4deaafbb18680e3143e8b471acd650ed6f72a408a33137f0a0dd104fbe7f8ca", "signature": "d4deaafbb18680e3143e8b471acd650ed6f72a408a33137f0a0dd104fbe7f8ca"}, "../node_modules/typescript/lib/lib.es2015.proxy.d.ts": {"version": "5e72f949a89717db444e3bd9433468890068bb21a5638d8ab15a1359e05e54fe", "signature": "5e72f949a89717db444e3bd9433468890068bb21a5638d8ab15a1359e05e54fe"}, "../node_modules/typescript/lib/lib.es2015.reflect.d.ts": {"version": "f5b242136ae9bfb1cc99a5971cccc44e99947ae6b5ef6fd8aa54b5ade553b976", "signature": "f5b242136ae9bfb1cc99a5971cccc44e99947ae6b5ef6fd8aa54b5ade553b976"}, "../node_modules/typescript/lib/lib.es2015.symbol.d.ts": {"version": "9ae2860252d6b5f16e2026d8a2c2069db7b2a3295e98b6031d01337b96437230", "signature": "9ae2860252d6b5f16e2026d8a2c2069db7b2a3295e98b6031d01337b96437230"}, "../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts": {"version": "3e0a459888f32b42138d5a39f706ff2d55d500ab1031e0988b5568b0f67c2303", "signature": "3e0a459888f32b42138d5a39f706ff2d55d500ab1031e0988b5568b0f67c2303"}, "../node_modules/typescript/lib/lib.es2016.array.include.d.ts": {"version": "3f96f1e570aedbd97bf818c246727151e873125d0512e4ae904330286c721bc0", "signature": "3f96f1e570aedbd97bf818c246727151e873125d0512e4ae904330286c721bc0"}, "../node_modules/typescript/lib/lib.es2017.object.d.ts": {"version": "c2d60b2e558d44384e4704b00e6b3d154334721a911f094d3133c35f0917b408", "signature": "c2d60b2e558d44384e4704b00e6b3d154334721a911f094d3133c35f0917b408"}, "../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts": {"version": "b8667586a618c5cf64523d4e500ae39e781428abfb28f3de441fc66b56144b6f", "signature": "b8667586a618c5cf64523d4e500ae39e781428abfb28f3de441fc66b56144b6f"}, "../node_modules/typescript/lib/lib.es2017.string.d.ts": {"version": "21df2e0059f14dcb4c3a0e125859f6b6ff01332ee24b0065a741d121250bc71c", "signature": "21df2e0059f14dcb4c3a0e125859f6b6ff01332ee24b0065a741d121250bc71c"}, "../node_modules/typescript/lib/lib.es2017.intl.d.ts": {"version": "c1759cb171c7619af0d2234f2f8fb2a871ee88e956e2ed91bb61778e41f272c6", "signature": "c1759cb171c7619af0d2234f2f8fb2a871ee88e956e2ed91bb61778e41f272c6"}, "../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts": {"version": "28569d59e07d4378cb3d54979c4c60f9f06305c9bb6999ffe6cab758957adc46", "signature": "28569d59e07d4378cb3d54979c4c60f9f06305c9bb6999ffe6cab758957adc46"}, "../node_modules/typescript/lib/lib.es2017.full.d.ts": {"version": "873c09f1c309389742d98b7b67419a8e0a5fa6f10ce59fd5149ecd31a2818594", "signature": "873c09f1c309389742d98b7b67419a8e0a5fa6f10ce59fd5149ecd31a2818594"}, "../node_modules/@types/estree/index.d.ts": {"version": "c2efad8a2f2d7fb931ff15c7959fb45340e74684cd665ddf0cbf9b3977be1644", "signature": "c2efad8a2f2d7fb931ff15c7959fb45340e74684cd665ddf0cbf9b3977be1644"}, "../src/walker.js": {"version": "4cc9d0e334d83a4cebeeac502de37a1aeeb953f6d4145a886d9eecea1f2142a7", "signature": "075872468ccc19c83b03fd717fc9305b5f8ec09592210cf60279cb13eca2bd70"}, "../src/async.js": {"version": "904efd145090ac40c3c98f29cc928332898a62ab642dd5921db2ae249bfe014a", "signature": "da428f781d6dc6dfd4f4afd0dd5f25a780897dc8b57e5b30462491b7d08f32c0"}, "../src/sync.js": {"version": "85bb22b85042f0a3717d8fac2fc8f62af16894652be34d1e08eb3e63785535f5", "signature": "5b131a727db18c956611a5e33d08217df96d0f2e0f26d98b804d1ec2407e59ae"}, "../src/index.js": {"version": "99128f4c6cb79cb1e3abf3f2ba96faedd2b820aab4fd7f743aab0b8d710a73af", "signature": "c52be5c79280bfcfcf359c084c6f2f70f405b0ad14dde96b6703dbc5ef2261f5"}}, "options": {"allowJs": true, "target": 4, "module": 99, "types": ["estree"], "declaration": true, "declarationDir": "./", "emitDeclarationOnly": true, "outDir": "./", "newLine": 1, "noImplicitAny": true, "noImplicitThis": true, "incremental": true, "configFilePath": "../tsconfig.json"}, "referencedMap": {"../src/walker.js": ["../node_modules/@types/estree/index.d.ts"], "../src/async.js": ["../src/walker.js", "../node_modules/@types/estree/index.d.ts"], "../src/sync.js": ["../src/walker.js", "../node_modules/@types/estree/index.d.ts"], "../src/index.js": ["../src/sync.js", "../src/async.js", "../node_modules/@types/estree/index.d.ts"]}, "exportedModulesMap": {"../src/walker.js": ["../node_modules/@types/estree/index.d.ts"], "../src/async.js": ["../node_modules/@types/estree/index.d.ts"], "../src/sync.js": ["../node_modules/@types/estree/index.d.ts"], "../src/index.js": ["../node_modules/@types/estree/index.d.ts"]}, "semanticDiagnosticsPerFile": ["../node_modules/typescript/lib/lib.es5.d.ts", "../node_modules/typescript/lib/lib.es2015.d.ts", "../node_modules/typescript/lib/lib.es2016.d.ts", "../node_modules/typescript/lib/lib.es2017.d.ts", "../node_modules/typescript/lib/lib.dom.d.ts", "../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../node_modules/typescript/lib/lib.webworker.importscripts.d.ts", "../node_modules/typescript/lib/lib.scripthost.d.ts", "../node_modules/typescript/lib/lib.es2015.core.d.ts", "../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../node_modules/typescript/lib/lib.es2017.object.d.ts", "../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../node_modules/typescript/lib/lib.es2017.string.d.ts", "../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../node_modules/typescript/lib/lib.es2017.full.d.ts", "../node_modules/@types/estree/index.d.ts", "../src/walker.js", ["../src/async.js", [{"file": "../src/async.js", "start": 864, "length": 12, "messageText": "'_should_skip' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/async.js", "start": 907, "length": 14, "messageText": "'_should_remove' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/async.js", "start": 954, "length": 12, "messageText": "'_replacement' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/async.js", "start": 991, "length": 24, "messageText": "'should_skip' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/async.js", "start": 1021, "length": 26, "messageText": "'should_remove' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/async.js", "start": 1053, "length": 23, "messageText": "'replacement' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/async.js", "start": 1643, "length": 9, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type 'BaseNode'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type 'BaseNode'.", "category": 1, "code": 7054}]}}]], ["../src/sync.js", [{"file": "../src/sync.js", "start": 837, "length": 12, "messageText": "'_should_skip' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/sync.js", "start": 880, "length": 14, "messageText": "'_should_remove' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/sync.js", "start": 927, "length": 12, "messageText": "'_replacement' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/sync.js", "start": 964, "length": 24, "messageText": "'should_skip' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/sync.js", "start": 994, "length": 26, "messageText": "'should_remove' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/sync.js", "start": 1026, "length": 23, "messageText": "'replacement' implicitly has type 'any' because it does not have a type annotation and is referenced directly or indirectly in its own initializer.", "category": 1, "code": 7022}, {"file": "../src/sync.js", "start": 1610, "length": 9, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type 'BaseNode'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type 'BaseNode'.", "category": 1, "code": 7054}]}}]], "../src/index.js"]}, "version": "3.7.5"}