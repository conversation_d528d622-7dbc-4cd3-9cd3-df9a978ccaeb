{"name": "@formkit/i18n", "version": "1.6.7", "type": "module", "description": "Internationalization layer for FormKit.", "main": "dist/index.cjs", "types": "dist/index.d.cts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/index.d.mts", "development": "./dist/index.dev.mjs", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./*": "./dist/*"}, "keywords": ["vue", "forms", "inputs", "validation"], "repository": {"type": "git", "url": "https://github.com/formkit/formkit.git", "directory": "packages/i18n"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"@formkit/core": "1.6.7", "@formkit/utils": "1.6.7", "@formkit/validation": "1.6.7"}, "scripts": {"test": "jest"}}