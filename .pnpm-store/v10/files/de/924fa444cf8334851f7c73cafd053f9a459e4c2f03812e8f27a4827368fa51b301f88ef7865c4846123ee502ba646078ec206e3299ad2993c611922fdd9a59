Object.defineProperty(exports, '__esModule', { value: true });

const aggregateErrors = require('./aggregate-errors.js');
const array = require('./array.js');
const browser = require('./browser.js');
const dsn = require('./dsn.js');
const error = require('./error.js');
const worldwide = require('./worldwide.js');
const console = require('./instrument/console.js');
const fetch = require('./instrument/fetch.js');
const globalError = require('./instrument/globalError.js');
const globalUnhandledRejection = require('./instrument/globalUnhandledRejection.js');
const handlers = require('./instrument/handlers.js');
const is = require('./is.js');
const isBrowser = require('./isBrowser.js');
const logger = require('./logger.js');
const memo = require('./memo.js');
const misc = require('./misc.js');
const node = require('./node.js');
const normalize = require('./normalize.js');
const object = require('./object.js');
const path = require('./path.js');
const promisebuffer = require('./promisebuffer.js');
const requestdata = require('./requestdata.js');
const severity = require('./severity.js');
const stacktrace = require('./stacktrace.js');
const nodeStackTrace = require('./node-stack-trace.js');
const string = require('./string.js');
const supports = require('./supports.js');
const syncpromise = require('./syncpromise.js');
const time = require('./time.js');
const tracing = require('./tracing.js');
const env = require('./env.js');
const envelope = require('./envelope.js');
const clientreport = require('./clientreport.js');
const ratelimit = require('./ratelimit.js');
const baggage = require('./baggage.js');
const url = require('./url.js');
const cache = require('./cache.js');
const eventbuilder = require('./eventbuilder.js');
const anr = require('./anr.js');
const lru = require('./lru.js');
const _asyncNullishCoalesce = require('./buildPolyfills/_asyncNullishCoalesce.js');
const _asyncOptionalChain = require('./buildPolyfills/_asyncOptionalChain.js');
const _asyncOptionalChainDelete = require('./buildPolyfills/_asyncOptionalChainDelete.js');
const _nullishCoalesce = require('./buildPolyfills/_nullishCoalesce.js');
const _optionalChain = require('./buildPolyfills/_optionalChain.js');
const _optionalChainDelete = require('./buildPolyfills/_optionalChainDelete.js');
const propagationContext = require('./propagationContext.js');
const version = require('./version.js');
const escapeStringForRegex = require('./vendor/escapeStringForRegex.js');
const supportsHistory = require('./vendor/supportsHistory.js');



exports.applyAggregateErrorsToEvent = aggregateErrors.applyAggregateErrorsToEvent;
exports.flatten = array.flatten;
exports.getComponentName = browser.getComponentName;
exports.getDomElement = browser.getDomElement;
exports.getLocationHref = browser.getLocationHref;
exports.htmlTreeAsString = browser.htmlTreeAsString;
exports.dsnFromString = dsn.dsnFromString;
exports.dsnToString = dsn.dsnToString;
exports.makeDsn = dsn.makeDsn;
exports.SentryError = error.SentryError;
exports.GLOBAL_OBJ = worldwide.GLOBAL_OBJ;
exports.getGlobalSingleton = worldwide.getGlobalSingleton;
exports.addConsoleInstrumentationHandler = console.addConsoleInstrumentationHandler;
exports.addFetchEndInstrumentationHandler = fetch.addFetchEndInstrumentationHandler;
exports.addFetchInstrumentationHandler = fetch.addFetchInstrumentationHandler;
exports.addGlobalErrorInstrumentationHandler = globalError.addGlobalErrorInstrumentationHandler;
exports.addGlobalUnhandledRejectionInstrumentationHandler = globalUnhandledRejection.addGlobalUnhandledRejectionInstrumentationHandler;
exports.addHandler = handlers.addHandler;
exports.maybeInstrument = handlers.maybeInstrument;
exports.resetInstrumentationHandlers = handlers.resetInstrumentationHandlers;
exports.triggerHandlers = handlers.triggerHandlers;
exports.isDOMError = is.isDOMError;
exports.isDOMException = is.isDOMException;
exports.isElement = is.isElement;
exports.isError = is.isError;
exports.isErrorEvent = is.isErrorEvent;
exports.isEvent = is.isEvent;
exports.isInstanceOf = is.isInstanceOf;
exports.isParameterizedString = is.isParameterizedString;
exports.isPlainObject = is.isPlainObject;
exports.isPrimitive = is.isPrimitive;
exports.isRegExp = is.isRegExp;
exports.isString = is.isString;
exports.isSyntheticEvent = is.isSyntheticEvent;
exports.isThenable = is.isThenable;
exports.isVueViewModel = is.isVueViewModel;
exports.isBrowser = isBrowser.isBrowser;
exports.CONSOLE_LEVELS = logger.CONSOLE_LEVELS;
exports.consoleSandbox = logger.consoleSandbox;
exports.logger = logger.logger;
exports.originalConsoleMethods = logger.originalConsoleMethods;
exports.memoBuilder = memo.memoBuilder;
exports.addContextToFrame = misc.addContextToFrame;
exports.addExceptionMechanism = misc.addExceptionMechanism;
exports.addExceptionTypeValue = misc.addExceptionTypeValue;
exports.arrayify = misc.arrayify;
exports.checkOrSetAlreadyCaught = misc.checkOrSetAlreadyCaught;
exports.getEventDescription = misc.getEventDescription;
exports.parseSemver = misc.parseSemver;
exports.uuid4 = misc.uuid4;
exports.dynamicRequire = node.dynamicRequire;
exports.isNodeEnv = node.isNodeEnv;
exports.loadModule = node.loadModule;
exports.normalize = normalize.normalize;
exports.normalizeToSize = normalize.normalizeToSize;
exports.normalizeUrlToBase = normalize.normalizeUrlToBase;
exports.addNonEnumerableProperty = object.addNonEnumerableProperty;
exports.convertToPlainObject = object.convertToPlainObject;
exports.dropUndefinedKeys = object.dropUndefinedKeys;
exports.extractExceptionKeysForMessage = object.extractExceptionKeysForMessage;
exports.fill = object.fill;
exports.getOriginalFunction = object.getOriginalFunction;
exports.markFunctionWrapped = object.markFunctionWrapped;
exports.objectify = object.objectify;
exports.urlEncode = object.urlEncode;
exports.basename = path.basename;
exports.dirname = path.dirname;
exports.isAbsolute = path.isAbsolute;
exports.join = path.join;
exports.normalizePath = path.normalizePath;
exports.relative = path.relative;
exports.resolve = path.resolve;
exports.makePromiseBuffer = promisebuffer.makePromiseBuffer;
exports.DEFAULT_USER_INCLUDES = requestdata.DEFAULT_USER_INCLUDES;
exports.addRequestDataToEvent = requestdata.addRequestDataToEvent;
exports.extractPathForTransaction = requestdata.extractPathForTransaction;
exports.extractRequestData = requestdata.extractRequestData;
exports.winterCGHeadersToDict = requestdata.winterCGHeadersToDict;
exports.winterCGRequestToRequestData = requestdata.winterCGRequestToRequestData;
exports.severityLevelFromString = severity.severityLevelFromString;
exports.validSeverityLevels = severity.validSeverityLevels;
exports.UNKNOWN_FUNCTION = stacktrace.UNKNOWN_FUNCTION;
exports.createStackParser = stacktrace.createStackParser;
exports.getFramesFromEvent = stacktrace.getFramesFromEvent;
exports.getFunctionName = stacktrace.getFunctionName;
exports.stackParserFromStackParserOptions = stacktrace.stackParserFromStackParserOptions;
exports.stripSentryFramesAndReverse = stacktrace.stripSentryFramesAndReverse;
exports.filenameIsInApp = nodeStackTrace.filenameIsInApp;
exports.node = nodeStackTrace.node;
exports.nodeStackLineParser = nodeStackTrace.nodeStackLineParser;
exports.isMatchingPattern = string.isMatchingPattern;
exports.safeJoin = string.safeJoin;
exports.snipLine = string.snipLine;
exports.stringMatchesSomePattern = string.stringMatchesSomePattern;
exports.truncate = string.truncate;
exports.isNativeFunction = supports.isNativeFunction;
exports.supportsDOMError = supports.supportsDOMError;
exports.supportsDOMException = supports.supportsDOMException;
exports.supportsErrorEvent = supports.supportsErrorEvent;
exports.supportsFetch = supports.supportsFetch;
exports.supportsNativeFetch = supports.supportsNativeFetch;
exports.supportsReferrerPolicy = supports.supportsReferrerPolicy;
exports.supportsReportingObserver = supports.supportsReportingObserver;
exports.SyncPromise = syncpromise.SyncPromise;
exports.rejectedSyncPromise = syncpromise.rejectedSyncPromise;
exports.resolvedSyncPromise = syncpromise.resolvedSyncPromise;
Object.defineProperty(exports, "_browserPerformanceTimeOriginMode", {
	enumerable: true,
	get: () => time._browserPerformanceTimeOriginMode
});
exports.browserPerformanceTimeOrigin = time.browserPerformanceTimeOrigin;
exports.dateTimestampInSeconds = time.dateTimestampInSeconds;
exports.timestampInSeconds = time.timestampInSeconds;
exports.TRACEPARENT_REGEXP = tracing.TRACEPARENT_REGEXP;
exports.extractTraceparentData = tracing.extractTraceparentData;
exports.generateSentryTraceHeader = tracing.generateSentryTraceHeader;
exports.propagationContextFromHeaders = tracing.propagationContextFromHeaders;
exports.getSDKSource = env.getSDKSource;
exports.isBrowserBundle = env.isBrowserBundle;
exports.addItemToEnvelope = envelope.addItemToEnvelope;
exports.createAttachmentEnvelopeItem = envelope.createAttachmentEnvelopeItem;
exports.createEnvelope = envelope.createEnvelope;
exports.createEventEnvelopeHeaders = envelope.createEventEnvelopeHeaders;
exports.createSpanEnvelopeItem = envelope.createSpanEnvelopeItem;
exports.envelopeContainsItemType = envelope.envelopeContainsItemType;
exports.envelopeItemTypeToDataCategory = envelope.envelopeItemTypeToDataCategory;
exports.forEachEnvelopeItem = envelope.forEachEnvelopeItem;
exports.getSdkMetadataForEnvelopeHeader = envelope.getSdkMetadataForEnvelopeHeader;
exports.parseEnvelope = envelope.parseEnvelope;
exports.serializeEnvelope = envelope.serializeEnvelope;
exports.createClientReportEnvelope = clientreport.createClientReportEnvelope;
exports.DEFAULT_RETRY_AFTER = ratelimit.DEFAULT_RETRY_AFTER;
exports.disabledUntil = ratelimit.disabledUntil;
exports.isRateLimited = ratelimit.isRateLimited;
exports.parseRetryAfterHeader = ratelimit.parseRetryAfterHeader;
exports.updateRateLimits = ratelimit.updateRateLimits;
exports.BAGGAGE_HEADER_NAME = baggage.BAGGAGE_HEADER_NAME;
exports.MAX_BAGGAGE_STRING_LENGTH = baggage.MAX_BAGGAGE_STRING_LENGTH;
exports.SENTRY_BAGGAGE_KEY_PREFIX = baggage.SENTRY_BAGGAGE_KEY_PREFIX;
exports.SENTRY_BAGGAGE_KEY_PREFIX_REGEX = baggage.SENTRY_BAGGAGE_KEY_PREFIX_REGEX;
exports.baggageHeaderToDynamicSamplingContext = baggage.baggageHeaderToDynamicSamplingContext;
exports.dynamicSamplingContextToSentryBaggageHeader = baggage.dynamicSamplingContextToSentryBaggageHeader;
exports.parseBaggageHeader = baggage.parseBaggageHeader;
exports.getNumberOfUrlSegments = url.getNumberOfUrlSegments;
exports.getSanitizedUrlString = url.getSanitizedUrlString;
exports.parseUrl = url.parseUrl;
exports.stripUrlQueryAndFragment = url.stripUrlQueryAndFragment;
exports.makeFifoCache = cache.makeFifoCache;
exports.eventFromMessage = eventbuilder.eventFromMessage;
exports.eventFromUnknownInput = eventbuilder.eventFromUnknownInput;
exports.exceptionFromError = eventbuilder.exceptionFromError;
exports.parseStackFrames = eventbuilder.parseStackFrames;
exports.callFrameToStackFrame = anr.callFrameToStackFrame;
exports.watchdogTimer = anr.watchdogTimer;
exports.LRUMap = lru.LRUMap;
exports._asyncNullishCoalesce = _asyncNullishCoalesce._asyncNullishCoalesce;
exports._asyncOptionalChain = _asyncOptionalChain._asyncOptionalChain;
exports._asyncOptionalChainDelete = _asyncOptionalChainDelete._asyncOptionalChainDelete;
exports._nullishCoalesce = _nullishCoalesce._nullishCoalesce;
exports._optionalChain = _optionalChain._optionalChain;
exports._optionalChainDelete = _optionalChainDelete._optionalChainDelete;
exports.generatePropagationContext = propagationContext.generatePropagationContext;
exports.SDK_VERSION = version.SDK_VERSION;
exports.escapeStringForRegex = escapeStringForRegex.escapeStringForRegex;
exports.supportsHistory = supportsHistory.supportsHistory;
//# sourceMappingURL=index.js.map
